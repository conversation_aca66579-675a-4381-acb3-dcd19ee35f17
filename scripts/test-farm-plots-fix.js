#!/usr/bin/env node

/**
 * 测试农场区块初始化修复
 * 验证当 farm_plots 表为空时，系统是否根据 farm_configs 表的配置数量创建对应数量的农场
 */

const axios = require('axios');
const mysql = require('mysql2/promise');
const jwt = require('jsonwebtoken');
require('dotenv').config();

// 配置
const baseUrl = 'http://localhost:3456/api';
const testWalletId = 999999; // 使用一个测试用的钱包ID

// 数据库配置
const dbConfig = {
  host: '127.0.0.1',
  port: 3669,
  user: 'wolf',
  password: '00321zixunadmin',
  database: 'wolf'
};

/**
 * 生成测试JWT token
 */
function generateTestToken(walletId, userId = null) {
  const JWT_SECRET = process.env.JWT_SECRET_Wallet;
  if (!JWT_SECRET) {
    throw new Error('JWT_SECRET_Wallet 环境变量未设置');
  }

  const payload = {
    userId: userId || walletId,
    walletId: walletId,
    walletAddress: `test_wallet_${walletId}`
  };

  return jwt.sign(payload, JWT_SECRET, { expiresIn: '60d' });
}

/**
 * 创建数据库连接
 */
async function createConnection() {
  try {
    const connection = await mysql.createConnection(dbConfig);
    console.log('✅ 数据库连接成功');
    return connection;
  } catch (error) {
    console.error('❌ 数据库连接失败:', error.message);
    throw error;
  }
}

/**
 * 创建测试用户
 */
async function createTestUser(connection) {
  try {
    // 检查用户是否已存在
    const [existing] = await connection.execute(
      'SELECT id FROM user_wallets WHERE id = ?',
      [testWalletId]
    );

    if (existing.length > 0) {
      console.log(`✅ 测试用户已存在: walletId=${testWalletId}`);
      return;
    }

    // 先创建 users 表记录
    const [userExists] = await connection.execute(
      'SELECT id FROM users WHERE id = ?',
      [testWalletId]
    );

    if (userExists.length === 0) {
      await connection.execute(`
        INSERT INTO users (id, telegramId, username, firstName, createdAt, updatedAt)
        VALUES (?, ?, ?, ?, NOW(), NOW())
      `, [testWalletId, `test_telegram_${testWalletId}`, `test_user_${testWalletId}`, 'Test User']);
      console.log(`✅ 测试用户基础记录创建成功: userId=${testWalletId}`);
    }

    // 创建测试用户钱包
    await connection.execute(`
      INSERT INTO user_wallets (id, userId, walletAddress, gem, milk, createdAt, updatedAt)
      VALUES (?, ?, ?, ?, ?, NOW(), NOW())
    `, [testWalletId, testWalletId, `test_wallet_${testWalletId}`, 1000000, 0]);

    console.log(`✅ 测试用户钱包创建成功: walletId=${testWalletId}`);
  } catch (error) {
    console.error('❌ 创建测试用户失败:', error.message);
    throw error;
  }
}

/**
 * 清理测试用户的农场数据
 */
async function cleanupUserFarmData(connection) {
  try {
    await connection.execute(
      'DELETE FROM farm_plots WHERE walletId = ?',
      [testWalletId]
    );
    console.log(`✅ 清理用户农场数据成功: walletId=${testWalletId}`);
  } catch (error) {
    console.error('❌ 清理用户农场数据失败:', error.message);
    throw error;
  }
}

/**
 * 获取激活的配置数量
 */
async function getActiveConfigCount(connection) {
  try {
    const [rows] = await connection.execute(
      'SELECT COUNT(*) as count FROM farm_configs WHERE isActive = true'
    );
    return rows[0].count;
  } catch (error) {
    console.error('❌ 获取配置数量失败:', error.message);
    throw error;
  }
}

/**
 * 获取用户的农场区块数量
 */
async function getUserFarmPlotCount(connection) {
  try {
    const [rows] = await connection.execute(
      'SELECT COUNT(*) as count FROM farm_plots WHERE walletId = ?',
      [testWalletId]
    );
    return rows[0].count;
  } catch (error) {
    console.error('❌ 获取用户农场区块数量失败:', error.message);
    throw error;
  }
}

/**
 * 测试农场区块API
 */
async function testFarmPlotsAPI() {
  try {
    console.log(`\n🧪 测试农场区块API...`);

    // 生成测试用户的JWT token
    const token = generateTestToken(testWalletId);
    console.log(`🔑 生成测试token: ${token.substring(0, 50)}...`);

    const response = await axios.get(`${baseUrl}/farm/farm-plots`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    console.log(`✅ API调用成功: ${response.status}`);
    console.log(`📊 返回的农场区块数量: ${response.data.data.farmPlots.length}`);

    if (response.data.data.farmPlots.length > 0) {
      const firstPlot = response.data.data.farmPlots[0];
      console.log(`📦 第一个区块信息:`);
      console.log(`   - 区块编号: ${firstPlot.plotNumber}`);
      console.log(`   - 等级: ${firstPlot.level}`);
      console.log(`   - 是否解锁: ${firstPlot.isUnlocked}`);
      console.log(`   - 奶牛数量: ${firstPlot.barnCount}`);
      console.log(`   - 产量: ${firstPlot.milkProduction}`);
      console.log(`   - 生产速度: ${firstPlot.productionSpeed}`);
    }

    return response.data.data.farmPlots.length;
  } catch (error) {
    console.error('❌ API调用失败:', error.response?.data?.message || error.message);
    throw error;
  }
}

/**
 * 主测试函数
 */
async function runTest() {
  let connection;
  
  try {
    console.log('🚀 开始农场区块初始化修复测试');
    console.log('='.repeat(60));
    
    // 创建数据库连接
    connection = await createConnection();
    
    // 创建测试用户
    await createTestUser(connection);
    
    // 获取当前激活的配置数量
    const configCount = await getActiveConfigCount(connection);
    console.log(`📊 当前激活的配置数量: ${configCount}`);
    
    if (configCount === 0) {
      console.log('⚠️ 没有激活的配置，请先运行配置初始化脚本');
      console.log('   node scripts/init-farm-config.js');
      return;
    }
    
    // 清理用户现有的农场数据
    console.log(`\n🧹 清理测试用户的农场数据...`);
    await cleanupUserFarmData(connection);
    
    // 验证清理结果
    const plotCountBefore = await getUserFarmPlotCount(connection);
    console.log(`✅ 清理后的农场区块数量: ${plotCountBefore}`);
    
    // 调用API触发初始化
    console.log(`\n🔄 调用API触发农场区块初始化...`);
    const apiPlotCount = await testFarmPlotsAPI();
    
    // 验证数据库中的结果
    const plotCountAfter = await getUserFarmPlotCount(connection);
    console.log(`\n📊 数据库中的农场区块数量: ${plotCountAfter}`);
    
    // 验证结果
    console.log(`\n🔍 验证结果:`);
    console.log(`   - 配置数量: ${configCount}`);
    console.log(`   - API返回的农场数量: ${apiPlotCount}`);
    console.log(`   - 数据库中的农场数量: ${plotCountAfter}`);
    
    if (plotCountAfter === configCount && apiPlotCount === configCount) {
      console.log(`\n✅ 测试通过！农场区块数量与配置数量一致`);
    } else {
      console.log(`\n❌ 测试失败！农场区块数量与配置数量不一致`);
      console.log(`   期望: ${configCount}, 实际: ${plotCountAfter}`);
    }
    
  } catch (error) {
    console.error('\n❌ 测试执行失败:', error.message);
  } finally {
    if (connection) {
      await connection.end();
      console.log('\n🔌 数据库连接已关闭');
    }
  }
}

/**
 * 清理测试数据
 */
async function cleanup() {
  let connection;
  
  try {
    console.log('🧹 清理测试数据...');
    connection = await createConnection();
    
    // 删除测试用户的农场数据
    await cleanupUserFarmData(connection);
    
    // 删除测试用户钱包
    await connection.execute(
      'DELETE FROM user_wallets WHERE id = ?',
      [testWalletId]
    );

    // 删除测试用户
    await connection.execute(
      'DELETE FROM users WHERE id = ?',
      [testWalletId]
    );
    
    console.log('✅ 测试数据清理完成');
  } catch (error) {
    console.error('❌ 清理测试数据失败:', error.message);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// 命令行参数处理
const args = process.argv.slice(2);

if (args.includes('--cleanup')) {
  cleanup();
} else {
  runTest();
}
